import { EntityComponentTypes } from "@minecraft/server";
import { stopPiglinChampionSounds, ATTACK_SOUND_MAP } from "./soundManager";
import { getTarget } from "../general_mechanics/targetUtils";
import { selectAttack } from "./controller";
import { executeHorizontalAttack } from "./attacks/horizontal";
import { executeVerticalAttack } from "./attacks/vertical";
import { executeFootStompAttack } from "./attacks/foot_stomp";
import { executeSpinSlamAttack } from "./attacks/spin_slam";
import { executeBodySlamAttack } from "./attacks/body_slam";
import { executeUpchuckAttack } from "./attacks/upchuck";
import { executeChargingAttack } from "./attacks/charging";
import { executeSummoningChantAttack } from "./attacks/summoning_chant";
import { executeHealingAbility } from "./abilities/healing";
import { executeStunnedStanding } from "./attacks/stunned_standing";
import { executeStunnedSitting } from "./attacks/stunned_sitting";
/**
 * Attack animation durations in ticks
 */
const ATTACK_ANIMATION_TIMES = {
    horizontal: 104,
    vertical: 106,
    foot_stomp: 80,
    spin_slam: 178,
    body_slam: 186,
    upchuck: 170,
    charging: 315,
    summoning_chant: 125,
    healing: 158,
    stunned_standing: 240,
    stunned_sitting: 333
};
/**
 * Executes the selected attack for the piglin champion
 * @param piglinChampion The piglin champion entity
 * @param attackType The type of attack to execute
 */
function executeSelectedAttack(piglinChampion, attackType) {
    console.warn('Attacking with : ' + attackType);
    // Apply slowness effect to prevent movement during the attack
    const animationTime = ATTACK_ANIMATION_TIMES[attackType];
    if (animationTime) {
        if (attackType === "charging") {
            // Apply slowness with amplifier 250 for only 30 ticks for charging attack
            piglinChampion.addEffect("minecraft:slowness", 30, { amplifier: 250, showParticles: false });
        }
        else {
            // Apply slowness with amplifier 250 for the duration of the attack
            piglinChampion.addEffect("minecraft:slowness", animationTime, { amplifier: 250, showParticles: false });
        }
    }
    // Execute the appropriate attack
    switch (attackType) {
        case "horizontal":
            executeHorizontalAttack(piglinChampion);
            break;
        case "vertical":
            const target = getTarget(piglinChampion, piglinChampion.location, 32, ["piglin_champion"]);
            if (target) {
                executeVerticalAttack(piglinChampion, target);
            }
            break;
        case "foot_stomp":
            executeFootStompAttack(piglinChampion);
            break;
        case "spin_slam":
            executeSpinSlamAttack(piglinChampion);
            break;
        case "body_slam":
            executeBodySlamAttack(piglinChampion);
            break;
        case "upchuck":
            executeUpchuckAttack(piglinChampion);
            break;
        case "charging":
            executeChargingAttack(piglinChampion);
            break;
        case "summoning_chant":
            const summonTarget = getTarget(piglinChampion, piglinChampion.location, 32, ["piglin_champion"]);
            executeSummoningChantAttack(piglinChampion, summonTarget);
            break;
        case "healing":
            executeHealingAbility(piglinChampion);
            break;
        case "stunned_standing":
            executeStunnedStanding(piglinChampion);
            break;
        case "stunned_sitting":
            executeStunnedSitting(piglinChampion);
            break;
        default:
            break;
    }
}
/**
 * Handles the piglin champion boss mechanics
 * @param piglinChampion The piglin champion entity
 * @param eventType The type of event ("attack" or "hurt")
 */
export function piglinChampionMechanics(piglinChampion, eventType = "attack") {
    // Handle hurt events for stun mechanics
    if (eventType === "hurt") {
        handleStunMechanics(piglinChampion);
        return;
    }
    // Skip if entity is not valid
    try {
        if (!piglinChampion)
            return;
        // Skip if entity is spawning or dead
        const isSpawning = piglinChampion.getProperty("ptd_dbb:spawning");
        const isDead = piglinChampion.getProperty("ptd_dbb:dead");
        if (isSpawning || isDead)
            return;
        // Get the current attack type
        const attackType = piglinChampion.getProperty("ptd_dbb:attack");
        // If we have an attack to execute, handle it
        if (attackType && attackType !== "none") {
            executeSelectedAttack(piglinChampion, attackType);
        }
        else {
            // No attack is currently active, handle attack selection and other mechanics
            handlePiglinChampionLogic(piglinChampion);
        }
    }
    catch (e) {
        return;
    }
    return;
}
/**
 * Handles piglin champion logic when no attack is active
 * This includes attack selection, healing, and target management
 * @param piglinChampion The piglin champion entity
 */
async function handlePiglinChampionLogic(piglinChampion) {
    try {
        const coolingDown = piglinChampion.getProperty("ptd_dbb:cooling_down");
        // Only proceed if not cooling down
        if (!coolingDown) {
            // Target detection and combat mechanics
            const target = getTarget(piglinChampion, piglinChampion.location, 32, ["piglin_champion"]);
            if (target) {
                // Check health for healing ability
                const healthComponent = piglinChampion.getComponent(EntityComponentTypes.Health);
                const health = healthComponent?.currentValue || 0;
                const maxHealth = healthComponent?.defaultValue || 0;
                const lastHealThreshold = piglinChampion.getProperty("ptd_dbb:last_heal_threshold");
                // Calculate current health percentage threshold (0-3 for 75%, 50%, 25%, 0%)
                const currentThreshold = Math.floor((health / maxHealth) * 4);
                // Check if we need to heal first
                if (currentThreshold < lastHealThreshold) {
                    // Update last heal threshold
                    piglinChampion.setProperty("ptd_dbb:last_heal_threshold", currentThreshold);
                    // Stop all other sound effects except for healing sound
                    const healingSound = ATTACK_SOUND_MAP["healing"];
                    stopPiglinChampionSounds(piglinChampion, healingSound);
                    // Trigger healing ability
                    piglinChampion.triggerEvent("ptd_dbb:healing_ability");
                }
                else {
                    // Otherwise select a normal attack
                    selectAttack(piglinChampion, target);
                    // After selecting attack, immediately execute it if one was selected
                    const selectedAttackType = piglinChampion.getProperty("ptd_dbb:attack");
                    if (selectedAttackType && selectedAttackType !== "none") {
                        executeSelectedAttack(piglinChampion, selectedAttackType);
                    }
                }
            }
        }
    }
    catch (e) {
        return;
    }
}
/**
 * Handles stun mechanics when the piglin champion takes damage
 * @param piglinChampion The piglin champion entity
 */
function handleStunMechanics(piglinChampion) {
    try {
        const attack = piglinChampion.getProperty("ptd_dbb:attack");
        const stunStandingTriggered = piglinChampion.getProperty("ptd_dbb:stun_standing_triggered");
        const stunSittingTriggered = piglinChampion.getProperty("ptd_dbb:stun_sitting_triggered");
        // Check health for stun mechanics
        const healthComponent = piglinChampion.getComponent(EntityComponentTypes.Health);
        const health = healthComponent?.currentValue || 0;
        const maxHealth = healthComponent?.defaultValue || 0;
        const healthPercentage = (health / maxHealth) * 100;
        // Check for stun standing at 65% health
        if (healthPercentage <= 65 && !stunStandingTriggered && attack !== "stunned_standing" && attack !== "stunned_sitting") {
            // Cancel any ongoing attack
            if (attack !== "none") {
                stopPiglinChampionSounds(piglinChampion);
                piglinChampion.triggerEvent("ptd_dbb:reset_attack");
            }
            // Stop all other sound effects except for stunned standing sound
            const stunSound = ATTACK_SOUND_MAP["stunned_standing"];
            stopPiglinChampionSounds(piglinChampion, stunSound);
            // Trigger stunned standing
            piglinChampion.triggerEvent("ptd_dbb:stunned_standing");
            // Execute the stun immediately
            executeStunnedStanding(piglinChampion);
            return;
        }
        // Check for stun sitting at 35% health
        if (healthPercentage <= 35 && !stunSittingTriggered && attack !== "stunned_sitting") {
            // Cancel any ongoing attack
            if (attack !== "none") {
                stopPiglinChampionSounds(piglinChampion);
                piglinChampion.triggerEvent("ptd_dbb:reset_attack");
            }
            // Stop all other sound effects except for stunned sitting sound
            const stunSound = ATTACK_SOUND_MAP["stunned_sitting"];
            stopPiglinChampionSounds(piglinChampion, stunSound);
            // Trigger stunned sitting
            piglinChampion.triggerEvent("ptd_dbb:stunned_sitting");
            // Execute the stun immediately
            executeStunnedSitting(piglinChampion);
            return;
        }
    }
    catch (e) {
        return;
    }
}
